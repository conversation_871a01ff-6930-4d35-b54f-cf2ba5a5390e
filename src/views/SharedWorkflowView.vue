<script setup lang="ts">
	import { ref, computed, onMounted } from "vue";
	import { useRoute, useRouter } from "vue-router";
	import { useI18n } from "vue-i18n";
	import { Error } from "@/utils/notify";
	import {
		PublicSharesAPI,
		type PublicShareInfo,
		type SharedWorkflowInfo,
	} from "@/api/shares";
	import {
		ShareIcon,
		EyeIcon,
		PlayIcon,
		DocumentDuplicateIcon,
		ExclamationTriangleIcon,
	} from "@heroicons/vue/24/outline";
	import SharedWorkflowRunner from "@/components/shares/SharedWorkflowRunner.vue";
	import SharedWorkflowViewer from "@/components/shares/SharedWorkflowViewer.vue";
	import RateLimitStatus from "@/components/shares/RateLimitStatus.vue";

	const { t } = useI18n();
	const route = useRoute();
	const router = useRouter();

	const shareUuid = route.params.shareUuid as string;
	const shareInfo = ref<PublicShareInfo | null>(null);
	const workflowInfo = ref<SharedWorkflowInfo | null>(null);
	const loading = ref(true);
	const error = ref<string | null>(null);
	const currentView = ref<"info" | "viewer" | "runner">("info");

	const isExpired = computed(() => {
		if (!shareInfo.value?.expires_at) return false;
		return new Date(shareInfo.value.expires_at) < new Date();
	});

	const canView = computed(() => {
		return shareInfo.value?.ui_config.allow_view && !isExpired.value;
	});

	const canRun = computed(() => {
		return shareInfo.value?.ui_config.allow_run && !isExpired.value;
	});

	const canCopy = computed(() => {
		return shareInfo.value?.ui_config.allow_copy && !isExpired.value;
	});

	const loadShareInfo = async () => {
		try {
			loading.value = true;
			error.value = null;

			const info = await PublicSharesAPI.getPublicShareInfo(shareUuid);
			shareInfo.value = info;

			// If user can view the workflow, load the workflow details
			if (canView.value) {
				const workflowData = await PublicSharesAPI.getSharedWorkflowInfo(shareUuid);
				workflowInfo.value = workflowData;
			}
		} catch (err) {
			console.error(err);
			error.value = err as string;
		} finally {
			loading.value = false;
		}
	};

	const handleViewWorkflow = () => {
		if (!canView.value) return;
		currentView.value = "viewer";
	};

	const handleRunWorkflow = () => {
		if (!canRun.value) return;
		currentView.value = "runner";
	};

	const handleCopyWorkflow = async () => {
		if (!canCopy.value) return;

		try {
			const copyData = {
				name: `Copy of ${shareInfo.value?.workflow_name}`,
				description: `Copied from shared workflow: ${shareInfo.value?.title}`,
			};

			const result = await PublicSharesAPI.copySharedWorkflow(shareUuid, copyData);

			// Redirect to the copied workflow
			router.push(`/projects/${result.workflow.uuid}`);
		} catch (err) {
			Error(t("error"), err as string);
		}
	};

	const formatDate = (dateString: string | undefined) => {
		if (!dateString) return "";
		return new Date(dateString).toLocaleString();
	};

	const getShareUrl = () => {
		return `${window.location.origin}/shared/${shareUuid}`;
	};

	onMounted(() => {
		loadShareInfo();
	});
</script>

<template>
	<div class="min-h-screen bg-base-200">
		<!-- Header -->
		<div class="navbar bg-base-100 border-b border-base-300">
			<div class="flex-1">
				<a href="/" class="btn btn-ghost normal-case text-xl">
					<ShareIcon class="w-6 h-6 mr-2" />
					{{ t("shares.shared_workflow") }}
				</a>
			</div>
			<div class="flex-none">
				<div class="flex items-center space-x-2">
					<button
						v-if="canView && currentView !== 'info'"
						class="btn btn-ghost btn-sm"
						@click="currentView = 'info'"
					>
						{{ t("shares.back_to_info") }}
					</button>
					<a href="/login" class="btn btn-primary btn-sm">
						{{ t("shares.login") }}
					</a>
				</div>
			</div>
		</div>

		<!-- Content -->
		<div class="container mx-auto px-4 py-8">
			<!-- Loading State -->
			<div v-if="loading" class="flex items-center justify-center py-20">
				<span class="loading loading-spinner loading-lg"></span>
			</div>

			<!-- Error State -->
			<div v-else-if="error" class="max-w-2xl mx-auto">
				<div class="alert alert-error">
					<ExclamationTriangleIcon class="w-6 h-6" />
					<div>
						<h3 class="font-bold">{{ t("shares.error_loading_share") }}</h3>
						<div class="text-sm">{{ error }}</div>
					</div>
				</div>
			</div>

			<!-- Share Info View -->
			<div v-else-if="currentView === 'info' && shareInfo" class="max-w-4xl mx-auto">
				<!-- Share Header -->
				<div class="card bg-base-100 shadow-lg mb-6">
					<div class="card-body">
						<div class="flex items-start justify-between">
							<div class="flex-1">
								<h1 class="text-3xl font-bold mb-2">{{ shareInfo?.title }}</h1>
								<p class="text-lg text-base-content/70 mb-4">
									{{ shareInfo?.description || t("shares.no_description") }}
								</p>
								<div
									class="flex items-center space-x-4 text-sm text-base-content/60"
								>
									<span
										>{{ t("shares.by") }} {{ shareInfo?.owner_name }}</span
									>
									<span
										>{{ t("shares.created") }}
										{{ formatDate(shareInfo?.created_at) }}</span
									>
									<span v-if="shareInfo?.expires_at" class="text-warning">
										{{ t("shares.expires") }}
										{{ formatDate(shareInfo.expires_at) }}
									</span>
								</div>
							</div>
							<div class="flex flex-col space-y-2">
								<div class="badge badge-outline">
									{{
										shareInfo?.bill_to === "sharer"
											? t("shares.free_to_use")
											: t("shares.pay_to_use")
									}}
								</div>
								<div v-if="isExpired" class="badge badge-error">
									{{ t("shares.expired") }}
								</div>
							</div>
						</div>
					</div>
				</div>

				<!-- Workflow Info -->
				<div class="card bg-base-100 shadow-lg mb-6">
					<div class="card-body">
						<h2 class="card-title text-xl mb-4">
							{{ t("shares.workflow_info") }}
						</h2>
						<div class="grid grid-cols-1 md:grid-cols-2 gap-4">
							<div>
								<h3 class="font-semibold mb-2">
									{{ shareInfo?.workflow_name }}
								</h3>
								<p class="text-base-content/70">
									{{
										shareInfo?.workflow_description ||
										t("shares.no_description")
									}}
								</p>
							</div>
							<div>
								<h3 class="font-semibold mb-2">
									{{ t("shares.available_actions") }}
								</h3>
								<div class="space-y-2">
									<div class="flex items-center space-x-2">
										<EyeIcon class="w-4 h-4" />
										<span>{{ t("shares.view") }}:</span>
										<span :class="canView ? 'text-success' : 'text-error'">
											{{
												canView
													? t("shares.available")
													: t("shares.unavailable")
											}}
										</span>
									</div>
									<div class="flex items-center space-x-2">
										<PlayIcon class="w-4 h-4" />
										<span>{{ t("shares.run") }}:</span>
										<span :class="canRun ? 'text-success' : 'text-error'">
											{{
												canRun
													? t("shares.available")
													: t("shares.unavailable")
											}}
										</span>
									</div>
									<div class="flex items-center space-x-2">
										<DocumentDuplicateIcon class="w-4 h-4" />
										<span>{{ t("shares.copy") }}:</span>
										<span :class="canCopy ? 'text-success' : 'text-error'">
											{{
												canCopy
													? t("shares.available")
													: t("shares.unavailable")
											}}
										</span>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>

				<!-- Rate Limiting Status -->
				<div
					v-if="canRun && shareInfo?.rate_limit?.enabled"
					class="card bg-base-100 shadow-lg mb-6"
				>
					<div class="card-body">
						<h2 class="card-title text-xl mb-4">{{ t("shares.usage_limits") }}</h2>
						<RateLimitStatus :rate-limit="shareInfo.rate_limit" />
					</div>
				</div>

				<!-- Welcome Message -->
				<div
					v-if="shareInfo?.ui_config?.welcome_message"
					class="card bg-base-100 shadow-lg mb-6"
				>
					<div class="card-body">
						<div class="prose max-w-none">
							<p>{{ shareInfo.ui_config.welcome_message }}</p>
						</div>
					</div>
				</div>

				<!-- Actions -->
				<div class="card bg-base-100 shadow-lg">
					<div class="card-body">
						<h2 class="card-title text-xl mb-4">
							{{ t("shares.what_would_you_like_to_do") }}
						</h2>
						<div class="grid grid-cols-1 md:grid-cols-3 gap-4">
							<button
								class="btn btn-outline btn-lg"
								:class="{ 'btn-disabled': !canView }"
								@click="handleViewWorkflow"
								:disabled="!canView"
							>
								<EyeIcon class="w-5 h-5 mr-2" />
								{{ t("shares.view_workflow") }}
							</button>
							<button
								class="btn btn-primary btn-lg"
								:class="{ 'btn-disabled': !canRun }"
								@click="handleRunWorkflow"
								:disabled="!canRun"
							>
								<PlayIcon class="w-5 h-5 mr-2" />
								{{ t("shares.run_workflow") }}
							</button>
							<button
								class="btn btn-secondary btn-lg"
								:class="{ 'btn-disabled': !canCopy }"
								@click="handleCopyWorkflow"
								:disabled="!canCopy"
							>
								<DocumentDuplicateIcon class="w-5 h-5 mr-2" />
								{{ t("shares.copy_workflow") }}
							</button>
						</div>
					</div>
				</div>
			</div>

			<!-- Workflow Viewer -->
			<SharedWorkflowViewer
				v-else-if="currentView === 'viewer' && workflowInfo"
				:workflow-info="workflowInfo"
				@back="currentView = 'info'"
			/>

			<!-- Workflow Runner -->
			<SharedWorkflowRunner
				v-else-if="currentView === 'runner' && workflowInfo"
				:workflow-info="workflowInfo"
				@back="currentView = 'info'"
			/>
		</div>
	</div>
</template>
